// This file is not needed since all /api/* requests are proxied to the backend API server
// See next.config.js for the proxy configuration
// The auth client in lib/auth-client.ts points to /api/auth which gets proxied to the backend

export async function GET() {
  return new Response('Auth API is handled by the backend server', {
    status: 404,
  });
}

export async function POST() {
  return new Response('Auth API is handled by the backend server', {
    status: 404,
  });
}
